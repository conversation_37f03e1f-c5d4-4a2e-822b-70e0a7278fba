import { ApiService } from './request';
import type {
  UserProfileResponse,
  UpdateProfileRequest,
  BooleanResponse,
  ApiResponse,
} from '@/types/api';
import { API_ENDPOINTS } from '@/types/api';
import type { LoginFormData, RegisterFormData } from '@/types';

/**
 * 用户相关 API 服务
 */
export const userApi = {
  /**
   * 获取当前用户资料
   * @returns 用户资料响应
   */
  async getProfile(): Promise<ApiResponse<UserProfileResponse>> {
    return ApiService.get(API_ENDPOINTS.USER.PROFILE);
  },

  /**
   * 更新当前用户资料
   * @param params 更新参数
   * @returns 布尔响应
   */
  async updateProfile(
    params: UpdateProfileRequest
  ): Promise<ApiResponse<BooleanResponse>> {
    return ApiService.put(API_ENDPOINTS.USER.PROFILE, params);
  },
};

/**
 * 用户工具函数
 */
export const userUtils = {
  /**
   * 将注册表单数据转换为API请求格式
   * @param formData 注册表单数据
   * @returns API请求格式的数据
   */
  convertRegisterFormToApiRequest(formData: RegisterFormData) {
    return {
      username: formData.email || '',
      password: formData.password || '',
      alias: formData.alias || '',
      profile: {
        firstName: formData.firstName || '',
        lastName: formData.lastName || '',
        addressLine1: formData.addressLine1,
        addressLine2: formData.addressLine2,
        stateProvince: formData.stateProvince || formData.state,
        countryCode: formData.countryCode,
        postalZipCode: formData.postalZipCode,
        avatarUrl: formData.avatarUrl,
        stageName: formData.stageName,
        bio: formData.bio,
      },
      defaultRoleId: formData.defaultRoleId || 'account.role.investor',
    };
  },

  /**
   * 将登录表单数据转换为API请求格式
   * @param formData 登录表单数据
   * @returns API请求格式的数据
   */
  convertLoginFormToApiRequest(formData: LoginFormData) {
    return {
      username: formData.email,
      password: formData.password,
    };
  },

  /**
   * 将用户资料响应转换为用户对象
   * @param profileResponse 用户资料响应
   * @returns 用户对象
   */
  convertProfileResponseToUser(profileResponse: UserProfileResponse) {
    return {
      accountId: profileResponse.accountId || '',
      email: profileResponse.email,
      alias: profileResponse.alias,
      firstName: profileResponse.firstName,
      lastName: profileResponse.lastName,
      mobile: profileResponse.mobile,
      addressLine1: profileResponse.addressLine1,
      addressLine2: profileResponse.addressLine2,
      stateProvince: profileResponse.stateProvince,
      countryCode: profileResponse.countryCode,
      postalZipCode: profileResponse.postalZipCode,
      avatarUrl: profileResponse.avatarUrl,
      stageName: profileResponse.stageName,
      bio: profileResponse.bio,
      displayName: profileResponse.displayName,
      roles: [], // 需要从其他接口获取
    };
  },
};

// 保持向后兼容的旧API
export const user = {
  register: (_params: RegisterFormData) => {
    // 这里可以调用新的认证API，但为了保持兼容性暂时保留
    console.warn(
      'user.register is deprecated, please use authApi.signup instead'
    );
    return userApi.getProfile(); // 临时实现
  },
  login: (_params: LoginFormData) => {
    // 这里可以调用新的认证API，但为了保持兼容性暂时保留
    console.warn('user.login is deprecated, please use authApi.login instead');
    return userApi.getProfile(); // 临时实现
  },
  getUserInfo: () => {
    console.warn(
      'user.getUserInfo is deprecated, please use userApi.getProfile instead'
    );
    return userApi.getProfile();
  },
};

// 导出默认对象
export default userApi;
