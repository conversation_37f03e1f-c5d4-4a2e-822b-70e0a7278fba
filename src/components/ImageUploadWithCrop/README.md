# ImageUploadWithCrop - 简化版图片上传裁切组件

一个简洁的头像上传裁切组件，专为个人项目设计。

## 功能特性

- ✅ 图片上传前裁切
- ✅ 圆形头像显示
- ✅ 头像回显
- ✅ 默认文字头像
- ✅ 自定义上传请求

## 基本用法

```tsx
import ImageUploadWithCrop from './components/ImageUploadWithCrop';

const MyComponent = () => {
  const handleUploadSuccess = (url: string) => {
    console.log('上传成功:', url);
  };

  const customUpload = (options: any) => {
    const { file, onSuccess, onError } = options;
    // 你的上传逻辑
    uploadToServer(file)
      .then(response => onSuccess({ url: response.url }))
      .catch(onError);
  };

  return (
    <ImageUploadWithCrop
      avatarUrl="https://example.com/avatar.jpg"
      avatarSize={100}
      userName="张三"
      onUploadSuccess={handleUploadSuccess}
      customRequest={customUpload}
    />
  );
};
```

## API

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `avatarUrl` | string | - | 头像地址（回显） |
| `avatarSize` | number | 100 | 头像大小 |
| `userName` | string | - | 用户名（默认头像） |
| `onUploadSuccess` | function | - | 上传成功回调 |
| `customRequest` | function | - | 自定义上传请求 |

## 在Profile中的使用

组件已集成到 `Profile.tsx` 中：

```tsx
<ImageUploadWithCrop
  avatarUrl={avatarUrl}
  avatarSize={120}
  userName={userInfo.name}
  onUploadSuccess={handleAvatarUploadSuccess}
  customRequest={customAvatarUpload}
/>
```

## 依赖

- `react-image-crop` - 图片裁切功能
- `antd` - UI组件库

## 特点

- 单文件实现，代码简洁
- 专注头像上传场景
- 支持圆形裁切
- 文件大小限制5MB
- 自动处理图片格式验证
