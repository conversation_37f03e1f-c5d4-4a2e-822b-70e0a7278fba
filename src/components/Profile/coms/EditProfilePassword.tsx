import React from 'react';
import PasswordForm from '@/components/PasswordForm';
import { Form } from 'antd';
import EditButton from './EditButton';

interface PasswordProps {
  visible: boolean;
  onClose: () => void;
}

const EditProfilePassword: React.FC<PasswordProps> = ({ visible, onClose }) => {
  if (!visible) return null;
  const [password, setPassword] = React.useState('');
  const [passwordForm] = Form.useForm();
  const onPasswordChange = (value: string) => {
    setPassword(value);
  };
  // 密码表单提交处理
  const onFinish = (values: any) => {
    console.log('密码表单提交:', values);

    // 这里可以调用API修改密码
    // setShowPasswordForm(false);
    // passwordForm.resetFields();
  };

  return (
    <div className="my-20px ">
      <Form
        form={passwordForm}
        layout="vertical"
        requiredMark={false}
        onFinish={onFinish}
        autoComplete="off"
        size="small"
      >
        <PasswordForm
          password={password}
          onPasswordChange={onPasswordChange}
          inputClassName="s-profile-input"
          labelClassName="text-label"
          showTermsCheckbox={false}
          className="border"
        />
        <EditButton
          onCancel={() => {
            onClose();
            passwordForm.resetFields();
          }}
        />
      </Form>
    </div>
  );
};

export default EditProfilePassword;
