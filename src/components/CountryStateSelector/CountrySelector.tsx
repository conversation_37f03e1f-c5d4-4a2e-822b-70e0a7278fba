import React from 'react';
import { Select } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { countryStateData } from './countryStateData';
// import styles from './CountryStateSelector.module.css';

interface CountrySelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}
const CountrySelector: React.FC<CountrySelectorProps> = ({
  placeholder,
  size = 'large',
  value,
  onChange,
  className,
}) => {
  const { t, isEnUS } = useLanguage();
  console.log('value----', value);

  const options = countryStateData.map(item => ({
    label: isEnUS ? item.label.en : item.label.zh,
    value: item.value,
  }));
  return (
    // <div className="s-form-selector-hover">

    <Select
      placeholder={placeholder || t('common.form.selectCountry')}
      size={size}
      className={
        className ||
        `s-form-selector-hover rounded-6px !h-54px text-14px flex-shrink-0 flex-basis-88px `
      }
      options={options}
      value={value}
      onChange={onChange}
    />
    // </div>
  );
};

export default CountrySelector;
