import React, { useEffect, useMemo, useRef } from 'react';
import { type FormInstance, Select, Form, ConfigProvider } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { countryStateData } from './countryStateData';
// import styles from './CountryStateSelector.module.css';

interface StateSelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  form: FormInstance;
  countryFieldName?: string; // 允许自定义 country 字段名
  stateFieldName?: string; // 允许自定义 state 字段名
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}

const StateSelector: React.FC<StateSelectorProps> = ({
  placeholder,
  size = 'large',
  form,
  countryFieldName = 'country',
  stateFieldName = 'state',
  value,
  onChange,
  className,
}) => {
  const { t, isEnUS } = useLanguage();
  const country = Form.useWatch(countryFieldName, form);
  const options = useMemo(() => {
    const state =
      countryStateData.find(item => item.value === country)?.children || [];
    const result = state.map(item => ({
      label: isEnUS ? item.label.en : item.label.zh,
      value: item.value,
    }));
    console.log('result---', result);

    return result;
  }, [country, form]);

  return (
    <Select
      placeholder={placeholder || t('common.form.selectState')}
      size={size}
      className={
        className ||
        `s-form-selector-hover rounded-6px !h-54px text-14px flex-shrink-0 flex-basis-88px `
      }
      options={options}
      value={value}
      onChange={onChange}
      disabled={!country} // 当没有选择国家时禁用
    />
  );
};

export default StateSelector;
